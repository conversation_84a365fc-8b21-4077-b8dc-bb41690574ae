<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Vue Component Logic</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .test-container {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #333;
            background: #f5f5f5;
        }
        .row-item {
            display: flex;
            min-height: 90px;
            border: 1px solid #ddd;
            margin: 10px 0;
        }
        .label {
            font-size: 20px;
            padding: 0 10px;
            width: 200px !important;
            background: #eee;
            display: flex;
            align-items: center;
        }
        .dynamic-font-size {
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 0;
            margin: 0;
            transform: translateY(-14px);
            word-wrap: break-word;
            white-space: normal;
            max-width: 490px;
            flex: 1;
            background: white;
        }
        .test-info {
            background: #fff3cd;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>Test Vue Component Enterprise Name Formatting</h1>
    
    <div class="test-container">
        <h2>Test Case 1: 10 ký tự (≤15) - 1 dòng, 32px</h2>
        <div class="test-info">Input: "株式会社テスト" (10 ký tự)</div>
        <div class="row-item">
            <span class="label">事業者名</span>
            <div class="dynamic-font-size" id="starting-enterprise-1">
                <!-- Text sẽ được xử lý bởi JavaScript -->
            </div>
        </div>
    </div>
    
    <div class="test-container">
        <h2>Test Case 2: 25 ký tự (16-30) - 2 dòng, 32px</h2>
        <div class="test-info">Input: "株式会社テストコンパニー長い名前" (25 ký tự)</div>
        <div class="row-item">
            <span class="label">事業者名</span>
            <div class="dynamic-font-size" id="starting-enterprise-2">
                <!-- Text sẽ được xử lý bởi JavaScript -->
            </div>
        </div>
    </div>
    
    <div class="test-container">
        <h2>Test Case 3: 45 ký tự (31-60) - 2 dòng, 16px</h2>
        <div class="test-info">Input: "株式会社テストコンパニー非常に長い名前の会社です" (45 ký tự)</div>
        <div class="row-item">
            <span class="label">事業者名</span>
            <div class="dynamic-font-size" id="starting-enterprise-3">
                <!-- Text sẽ được xử lý bởi JavaScript -->
            </div>
        </div>
    </div>
    
    <div class="test-container">
        <h2>Test Case 4: 75 ký tự (61-90) - 3 dòng, 16px</h2>
        <div class="test-info">Input: "株式会社テストコンパニー非常に長い名前の会社ですが更に長くなります" (75 ký tự)</div>
        <div class="row-item">
            <span class="label">事業者名</span>
            <div class="dynamic-font-size" id="starting-enterprise-4">
                <!-- Text sẽ được xử lý bởi JavaScript -->
            </div>
        </div>
    </div>
    
    <div class="test-container">
        <h2>Test Case 5: 100 ký tự (91-102) - 4 dòng, 16px</h2>
        <div class="test-info">Input: "株式会社テストコンパニー非常に長い名前の会社ですが更に長くなりますので最大文字数テスト" (100 ký tự)</div>
        <div class="row-item">
            <span class="label">事業者名</span>
            <div class="dynamic-font-size" id="starting-enterprise-5">
                <!-- Text sẽ được xử lý bởi JavaScript -->
            </div>
        </div>
    </div>

    <script>
        // Copy exact logic from Vue component
        const formatEnterpriseText = (text) => {
            if (!text) return '';
            
            const length = text.length;
            
            if (length <= 15) {
                // 15 chữ trở xuống: 1 dòng, cỡ chữ bình thường
                return text;
            } else if (length <= 30) {
                // 16-30 chữ: 2 dòng, cỡ chữ bình thường
                const line1 = text.substring(0, 15);
                const line2 = text.substring(15, 30);
                return `${line1}<br/>${line2}`;
            } else if (length <= 60) {
                // 31-60 chữ: 2 dòng, cỡ chữ nhỏ (một nửa)
                const line1 = text.substring(0, 30);
                const line2 = text.substring(30, 60);
                return `${line1}<br/>${line2}`;
            } else if (length <= 90) {
                // 61-90 chữ: 3 dòng, cỡ chữ nhỏ (một nửa)
                const line1 = text.substring(0, 30);
                const line2 = text.substring(30, 60);
                const line3 = text.substring(60, 90);
                return `${line1}<br/>${line2}<br/>${line3}`;
            } else if (length <= 102) {
                // 91-102 chữ: 4 dòng, cỡ chữ nhỏ (một nửa)
                const line1 = text.substring(0, 30);
                const line2 = text.substring(30, 60);
                const line3 = text.substring(60, 90);
                const line4 = text.substring(90, 102);
                return `${line1}<br/>${line2}<br/>${line3}<br/>${line4}`;
            }
            
            // Trường hợp vượt quá 102 ký tự (không nên xảy ra theo yêu cầu)
            return text.substring(0, 102);
        };

        const adjustFontSizeForElement = (elementId, text) => {
            const element = document.getElementById(elementId);
            if (element && text) {
                const textLength = text.length;
                const formattedText = formatEnterpriseText(text);
                element.innerHTML = formattedText;

                if (textLength <= 30) {
                    // Cỡ chữ bình thường (32px)
                    element.style.fontSize = '32px';
                    element.style.lineHeight = '40px';
                    element.style.transform = 'translateY(-14px)';
                } else {
                    // Cỡ chữ nhỏ (16px - một nửa của 32px)
                    element.style.fontSize = '16px';
                    element.style.lineHeight = '20px';
                    element.style.transform = 'translateY(-8px)';
                }
                element.style.textAlign = 'center';
                element.style.justifyContent = 'center';
            }
        };

        // Test cases
        const testCases = [
            '株式会社テスト', // 10 ký tự
            '株式会社テストコンパニー長い名前', // 25 ký tự
            '株式会社テストコンパニー非常に長い名前の会社です', // 45 ký tự
            '株式会社テストコンパニー非常に長い名前の会社ですが更に長くなります', // 75 ký tự
            '株式会社テストコンパニー非常に長い名前の会社ですが更に長くなりますので最大文字数テスト' // 100 ký tự
        ];

        // Apply formatting
        testCases.forEach((testCase, index) => {
            adjustFontSizeForElement(`starting-enterprise-${index + 1}`, testCase);
        });
    </script>
</body>
</html>
