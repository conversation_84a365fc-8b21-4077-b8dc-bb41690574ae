<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Enterprise Name Formatting</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .test-case {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ccc;
            background: #f9f9f9;
        }
        .dynamic-font-size {
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            min-height: 60px;
            max-width: 490px;
            word-wrap: break-word;
            white-space: normal;
            background: white;
        }
        .info {
            color: #666;
            font-size: 12px;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <h1>Test Enterprise Name Formatting Logic</h1>
    
    <div class="test-case">
        <h3>Test 1: 10 ký tự (≤15) - 1 dòng, 32px</h3>
        <div class="dynamic-font-size" id="test1"></div>
        <div class="info" id="info1"></div>
    </div>
    
    <div class="test-case">
        <h3>Test 2: 25 ký tự (16-30) - 2 dòng, 32px</h3>
        <div class="dynamic-font-size" id="test2"></div>
        <div class="info" id="info2"></div>
    </div>
    
    <div class="test-case">
        <h3>Test 3: 45 ký tự (31-60) - 2 dòng, 16px</h3>
        <div class="dynamic-font-size" id="test3"></div>
        <div class="info" id="info3"></div>
    </div>
    
    <div class="test-case">
        <h3>Test 4: 75 ký tự (61-90) - 3 dòng, 16px</h3>
        <div class="dynamic-font-size" id="test4"></div>
        <div class="info" id="info4"></div>
    </div>
    
    <div class="test-case">
        <h3>Test 5: 100 ký tự (91-102) - 4 dòng, 16px</h3>
        <div class="dynamic-font-size" id="test5"></div>
        <div class="info" id="info5"></div>
    </div>

    <script>
        // Function để format text theo yêu cầu (copy từ Vue component)
        const formatEnterpriseText = (text) => {
            if (!text) return '';
            
            const length = text.length;
            
            if (length <= 15) {
                // 15 chữ trở xuống: 1 dòng, cỡ chữ bình thường
                return text;
            } else if (length <= 30) {
                // 16-30 chữ: 2 dòng, cỡ chữ bình thường
                const line1 = text.substring(0, 15);
                const line2 = text.substring(15, 30);
                return `${line1}<br/>${line2}`;
            } else if (length <= 60) {
                // 31-60 chữ: 2 dòng, cỡ chữ nhỏ (một nửa)
                const line1 = text.substring(0, 30);
                const line2 = text.substring(30, 60);
                return `${line1}<br/>${line2}`;
            } else if (length <= 90) {
                // 61-90 chữ: 3 dòng, cỡ chữ nhỏ (một nửa)
                const line1 = text.substring(0, 30);
                const line2 = text.substring(30, 60);
                const line3 = text.substring(60, 90);
                return `${line1}<br/>${line2}<br/>${line3}`;
            } else if (length <= 102) {
                // 91-102 chữ: 4 dòng, cỡ chữ nhỏ (một nửa)
                const line1 = text.substring(0, 30);
                const line2 = text.substring(30, 60);
                const line3 = text.substring(60, 90);
                const line4 = text.substring(90, 102);
                return `${line1}<br/>${line2}<br/>${line3}<br/>${line4}`;
            }
            
            // Trường hợp vượt quá 102 ký tự (không nên xảy ra theo yêu cầu)
            return text.substring(0, 102);
        };

        // Test cases
        const testCases = [
            '株式会社テスト', // 10 ký tự
            '株式会社テストコンパニー長い名前', // 25 ký tự
            '株式会社テストコンパニー非常に長い名前の会社です', // 45 ký tự
            '株式会社テストコンパニー非常に長い名前の会社ですが更に長くなります', // 75 ký tự
            '株式会社テストコンパニー非常に長い名前の会社ですが更に長くなりますので最大文字数テスト' // 100 ký tự
        ];

        // Apply formatting to each test case
        testCases.forEach((testCase, index) => {
            const element = document.getElementById(`test${index + 1}`);
            const infoElement = document.getElementById(`info${index + 1}`);
            const length = testCase.length;
            
            // Format text
            const formattedText = formatEnterpriseText(testCase);
            element.innerHTML = formattedText;
            
            // Apply font size based on length
            if (length <= 30) {
                // Cỡ chữ bình thường (32px)
                element.style.fontSize = '32px';
                element.style.lineHeight = '40px';
                element.style.transform = 'translateY(-14px)';
            } else {
                // Cỡ chữ nhỏ (16px - một nửa của 32px)
                element.style.fontSize = '16px';
                element.style.lineHeight = '20px';
                element.style.transform = 'translateY(-8px)';
            }
            
            // Center alignment
            element.style.textAlign = 'center';
            element.style.justifyContent = 'center';
            
            // Show info
            infoElement.innerHTML = `Độ dài: ${length} ký tự, Font size: ${element.style.fontSize}`;
        });
    </script>
</body>
</html>
